import { Region, RegionalMockProvider } from '@npco/component-dto-test-utils/dist/regionMockProvider';

import type { SurchargesTaxesSettings } from '../types';

import { taxes } from './mockdata.taxes';

const defaultConfig: SurchargesTaxesSettings = {
  surchargeAllowed: true,
  surchargeEnabled: false,
  surchargePercent: 1,
  feePercent: 1,
  gstEnabled: false,
  gstPercent: 11,
  taxes,
  surchargeFullFees: false,
  surchargeEnabledMoto: false,
  surchargeFullFeesMoto: false,
  surchargePercentMoto: 13,
  feePercentMoto: 12,

  feesSurchargeCp: {
    surchargeEnabled: true,
    feeFixed: 10,
    surchargePercent: 1,
    feePercent: 1,
    surchargeFullFees: true,
  },
  feesSurchargeMoto: {
    surchargeEnabled: true,
    surchargePercent: 13,
    feeFixed: 10,
    feePercent: 12,
    surchargeFullFees: true,
  },
  feesSurchargeXinv: {
    surchargeEnabled: true,
    surchargePercent: 18,
    feePercent: 19,
    feeFixed: 20,
    surchargeFullFees: true,
    surchargePercentIntl: 21,
    feePercentIntl: 22,
    feeFixedIntl: 23,
  },
  feesSurchargeZinv: {
    surchargeEnabled: true,
    surchargePercent: 24,
    feePercent: 25,
    surchargeFullFees: true,
    surchargePercentIntl: 26,
    feePercentIntl: 27,
    feeFixed: 28,
    feeFixedIntl: 29,
  },
  feesSurchargeCpoc: {
    surchargeEnabled: true,
    surchargePercent: 30,
    feePercent: 31,
    surchargeFullFees: true,
    feeFixed: 32,
  },

  feesSurchargeVt: {
    surchargeEnabled: true,
    surchargePercent: 2,
    feePercent: 2,
    surchargeFullFees: true,
    feeFixed: 32,
  },

  feesSurchargePbl: {
    surchargeEnabled: true,
    surchargePercent: 38,
    feePercent: 39,
    feeFixed: 40,
    surchargeFullFees: true,
    surchargePercentIntl: 41,
    feePercentIntl: 42,
    feeFixedIntl: 43,
  },
};
const regionOverrides = {
  [Region.UK]: {
    surchargeAllowe: false,
    gstPercent: 2000,
  },
};
class SurchargesTaxesProvider extends RegionalMockProvider<SurchargesTaxesSettings> {
  constructor() {
    super(defaultConfig, regionOverrides);
  }
}
