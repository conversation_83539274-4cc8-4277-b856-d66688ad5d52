import lodash from 'lodash';

export enum Region {
  AUSTRALIA = 'ap-southeast-2',
  SINGAPORE = 'ap-southeast-1',
  UK = 'eu-west-2',
  GERMANY = 'eu-central-1',
  US = 'us-east-1',
}

export const getCurrentRegion = (): Region => (process.env.AWS_REGION as Region) ?? Region.AUSTRALIA;

type DeepPartial<T> = { [P in keyof T]?: DeepPartial<T[P]> };

export abstract class RegionalMockProvider<T extends Record<string, any>> {
  private readonly regionConfigs = new Map<Region, T>();

  private readonly defaultConfig: T;

  protected constructor(defaultConfig: T, regionOverrides: Partial<Record<Region, DeepPartial<T>>>) {
    this.defaultConfig = defaultConfig;

    // Pre‑compute configs
    for (const region of Object.values(Region)) {
      const override = regionOverrides[region];
      const merged = lodash.mergeWith({}, defaultConfig, override ?? {}, (_obj, src) =>
        Array.isArray(src) ? [...src] : undefined,
      );
      this.regionConfigs.set(region, merged);
    }
  }

  public getConfig(region: Region = getCurrentRegion()): T {
    const cfg = this.regionConfigs.get(region);
    return structuredClone(cfg ?? lodash.merge({}, this.defaultConfig));
  }
}
